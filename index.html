<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nạp Xu <PERSON> - Gi<PERSON> Rẻ</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <img src="assets/images/tiktok.svg" alt="TikTok Logo" class="logo-img">
                    <span class="logo-text">TikTok Coin</span>
                </div>

                <div class="user-menu">
                    <div class="user-avatar" id="userAvatar">
                        <img src="https://api.dicebear.com/8.x/initials/svg?seed=User" alt="Avatar">
                        <i class="fas fa-chevron-down"></i>
                    </div>

                    <div class="dropdown-menu" id="dropdownMenu">
                        <ul>
                            <li><a href="#" id="accountInfo"><i class="fas fa-user"></i> Tài khoản</a></li>
                            <li><a href="#" id="transactionHistory"><i class="fas fa-history"></i> Lịch sử nạp</a></li>
                            <li class="divider"></li>
                            <li><a href="#" id="logout"><i class="fas fa-sign-out-alt"></i> Đăng xuất</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Demo Notice -->
    <div class="demo-notice" id="demoNotice" style="display: none;">
        <div class="container">
            <div class="demo-content">
                <i class="fas fa-info-circle"></i>
                <span>Đang chạy ở chế độ DEMO - Dữ liệu được mô phỏng</span>
                <button class="demo-close" onclick="hideDemoNotice()">&times;</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <div class="content-wrapper">
                <!-- Left Content -->
                <div class="left-content">
                    <!-- Step 1: Username Input -->
                    <section class="step-section" id="step1">
                        <h3 class="step-title">1. Nhập Username Tiktok</h3>
                        <div class="username-form">
                            <input type="text" id="tiktokUsername" placeholder="Nhập username TikTok của bạn" class="form-input">
                            <button type="button" id="checkUsername" class="btn btn-primary">Kiểm tra</button>
                        </div>
                        <div class="username-result" id="usernameResult"></div>
                    </section>

                    <!-- Step 2: Package Selection -->
                    <section class="step-section" id="step2">
                        <h3 class="step-title">2. Chọn gói xu</h3>
                        <div class="package-loading" id="packageLoading">
                            <i class="fas fa-spinner fa-spin"></i> Đang tải...
                        </div>
                        <div class="package-grid" id="packageGrid" style="display: none;">
                            <!-- Packages will be loaded here -->
                        </div>
                    </section>

                    <!-- Step 3: Payment Method -->
                    <section class="step-section" id="step3">
                        <h3 class="step-title">3. Chọn phương thức thanh toán</h3>

                        <div class="payment-tabs">
                            <button class="tab-btn active" data-tab="card">Thẻ cào</button>
                            <button class="tab-btn" data-tab="transfer">Chuyển khoản</button>
                        </div>

                        <!-- Card Payment Form -->
                        <div class="payment-form active" id="cardPayment">
                            <div class="provider-grid">
                                <div class="provider-item" data-provider="viettel">
                                    <img src="assets/images/viettel.png" alt="Viettel">
                                    <span>Viettel</span>
                                </div>
                                <div class="provider-item" data-provider="vinaphone">
                                    <img src="assets/images/vinaphone.png" alt="VinaPhone">
                                    <span>VinaPhone</span>
                                </div>
                                <div class="provider-item" data-provider="mobifone">
                                    <img src="assets/images/mobifone.png" alt="Mobifone">
                                    <span>Mobifone</span>
                                </div>
                                <div class="provider-item" data-provider="zing">
                                    <img src="assets/images/zing.png" alt="Zing">
                                    <span>Zing</span>
                                </div>
                                <div class="provider-item" data-provider="garena">
                                    <img src="assets/images/garena.png" alt="Garena">
                                    <span>Garena</span>
                                </div>
                            </div>

                            <div class="card-form">
                                <div class="form-group">
                                    <label>Mệnh giá thẻ cào</label>
                                    <select id="cardValue" class="form-select">
                                        <option value="">Chọn mệnh giá</option>
                                        <option value="10000">10,000 VNĐ</option>
                                        <option value="20000">20,000 VNĐ</option>
                                        <option value="50000">50,000 VNĐ</option>
                                        <option value="100000">100,000 VNĐ</option>
                                        <option value="200000">200,000 VNĐ</option>
                                        <option value="500000">500,000 VNĐ</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>Số Seri</label>
                                    <input type="text" id="cardSerial" placeholder="Nhập số seri" class="form-input">
                                </div>

                                <div class="form-group">
                                    <label>Mã thẻ</label>
                                    <input type="text" id="cardCode" placeholder="Nhập mã thẻ" class="form-input">
                                </div>

                                <div class="form-group">
                                    <label>Username Nick TikTok *</label>
                                    <input type="text" id="cardUsername" placeholder="Username TikTok" class="form-input">
                                </div>

                                <button type="button" id="submitCard" class="btn btn-success btn-full">
                                    Xác nhận nạp thẻ cào
                                </button>
                            </div>
                        </div>

                        <!-- Transfer Payment Form -->
                        <div class="payment-form" id="transferPayment">
                            <div class="transfer-info">
                                <p>Số tiền cần chuyển: <strong id="transferAmount">0 VNĐ</strong></p>
                            </div>

                            <div class="form-group">
                                <label>Username Nick TikTok *</label>
                                <input type="text" id="transferUsername" placeholder="Username TikTok" class="form-input">
                            </div>

                            <button type="button" id="getTransferInfo" class="btn btn-primary btn-full">
                                Lấy thông tin chuyển khoản
                            </button>
                        </div>
                    </section>

                    <!-- Transaction History -->
                    <section class="step-section" id="historySection">
                        <h3 class="step-title">Lịch sử giao dịch</h3>
                        <div class="transaction-table">
                            <div class="no-transactions" id="noTransactions">
                                Chưa có giao dịch.
                            </div>
                            <table id="transactionTable" style="display: none;">
                                <thead>
                                    <tr>
                                        <th>Thời gian</th>
                                        <th>Username</th>
                                        <th>Gói xu</th>
                                        <th>Phương thức</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody id="transactionBody">
                                </tbody>
                            </table>
                        </div>
                    </section>
                </div>

                <!-- Right Sidebar -->
                <div class="right-sidebar">
                    <div class="order-summary">
                        <h3 class="summary-title">Tóm tắt đơn hàng</h3>

                        <div class="summary-content" id="summaryContent">
                            <p class="summary-placeholder">Vui lòng chọn tài khoản và gói xu.</p>
                        </div>

                        <div class="summary-details" id="summaryDetails" style="display: none;">
                            <div class="summary-item">
                                <span>Tài khoản:</span>
                                <span id="summaryUsername">-</span>
                            </div>
                            <div class="summary-item">
                                <span>Gói xu:</span>
                                <span id="summaryPackage">-</span>
                            </div>
                            <div class="summary-item">
                                <span>Thanh toán:</span>
                                <span id="summaryPrice">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>Thiết kế & phát triển bởi <a href="https://t.me/vankhanh1996" target="_blank">DVNTEAM</a></p>
                <p class="disclaimer">We only write code and are not responsible if this website violates the law.</p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <div class="modal" id="accountModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Thông tin tài khoản</h3>
                <button class="modal-close" id="closeAccountModal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="account-info">
                    <p>Đang tải thông tin...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
</body>
</html>
