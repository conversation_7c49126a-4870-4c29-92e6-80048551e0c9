# TikTok Recharge System - Environment Configuration
# Copy this file to .env and update the values

# Application
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=tiktok_recharge
DB_USERNAME=root
DB_PASSWORD=

# Payment Gateway Settings
# Viettel Card API
VIETTEL_API_URL=
VIETTEL_API_KEY=
VIETTEL_PARTNER_ID=

# VinaPhone Card API
VINAPHONE_API_URL=
VINAPHONE_API_KEY=
VINAPHONE_PARTNER_ID=

# Mobifone Card API
MOBIFONE_API_URL=
MOBIFONE_API_KEY=
MOBIFONE_PARTNER_ID=

# Bank Transfer Settings
BANK_NAME=Vietcombank
BANK_ACCOUNT_NUMBER=**********
BANK_ACCOUNT_NAME=NGUYEN VAN A
BANK_BRANCH=Ho Chi Minh City

# Security Settings
JWT_SECRET=your-jwt-secret-key-here
ENCRYPTION_KEY=your-encryption-key-here
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# Notification Settings
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=
EMAIL_SMTP_HOST=
EMAIL_SMTP_PORT=587
EMAIL_SMTP_USERNAME=
EMAIL_SMTP_PASSWORD=
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=TikTok Recharge

# TikTok API (if available)
TIKTOK_API_URL=
TIKTOK_API_KEY=
TIKTOK_CLIENT_ID=
TIKTOK_CLIENT_SECRET=

# Cache Settings
CACHE_DRIVER=file
CACHE_TTL=3600

# Session Settings
SESSION_LIFETIME=120
SESSION_ENCRYPT=true

# File Upload Settings
MAX_UPLOAD_SIZE=10M
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10M
LOG_MAX_FILES=5

# Admin Panel
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_2FA_ENABLED=false

# API Settings
API_RATE_LIMIT=60
API_TIMEOUT=30
API_VERSION=v1

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE=Website đang bảo trì, vui lòng quay lại sau.

# Analytics
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=

# CDN Settings
CDN_URL=
CDN_ENABLED=false

# Backup Settings
BACKUP_ENABLED=true
BACKUP_SCHEDULE=daily
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE=local
