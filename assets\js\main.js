// Global variables
let selectedPackage = null;
let selectedProvider = null;
let currentUser = null;

// DOM Elements
const userAvatar = document.getElementById('userAvatar');
const dropdownMenu = document.getElementById('dropdownMenu');
const tiktokUsername = document.getElementById('tiktokUsername');
const checkUsername = document.getElementById('checkUsername');
const usernameResult = document.getElementById('usernameResult');
const packageLoading = document.getElementById('packageLoading');
const packageGrid = document.getElementById('packageGrid');
const tabBtns = document.querySelectorAll('.tab-btn');
const paymentForms = document.querySelectorAll('.payment-form');
const providerItems = document.querySelectorAll('.provider-item');
const summaryContent = document.getElementById('summaryContent');
const summaryDetails = document.getElementById('summaryDetails');
const accountModal = document.getElementById('accountModal');
const closeAccountModal = document.getElementById('closeAccountModal');

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    loadPackages();
    setupEventListeners();
});

// Demo notice functions
function showDemoNotice() {
    const demoNotice = document.getElementById('demoNotice');
    if (demoNotice) {
        demoNotice.style.display = 'block';
    }
}

function hideDemoNotice() {
    const demoNotice = document.getElementById('demoNotice');
    if (demoNotice) {
        demoNotice.style.display = 'none';
    }
}

function initializeApp() {
    // Set default user
    currentUser = {
        username: 'User',
        avatar: 'https://api.dicebear.com/8.x/initials/svg?seed=User',
        balance: 0,
        transactions: []
    };

    // Update UI
    updateUserDisplay();
}

function setupEventListeners() {
    // User menu dropdown
    userAvatar.addEventListener('click', function(e) {
        e.stopPropagation();
        dropdownMenu.classList.toggle('show');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        dropdownMenu.classList.remove('show');
    });

    // Username check
    checkUsername.addEventListener('click', handleUsernameCheck);
    tiktokUsername.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleUsernameCheck();
        }
    });

    // Payment tabs
    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const tabName = this.dataset.tab;
            switchPaymentTab(tabName);
        });
    });

    // Provider selection
    providerItems.forEach(item => {
        item.addEventListener('click', function() {
            selectProvider(this.dataset.provider);
        });
    });

    // Modal events
    document.getElementById('accountInfo').addEventListener('click', showAccountModal);
    closeAccountModal.addEventListener('click', hideAccountModal);

    // Form submissions
    document.getElementById('submitCard').addEventListener('click', handleCardPayment);
    document.getElementById('getTransferInfo').addEventListener('click', handleTransferInfo);

    // Auto-fill username fields
    tiktokUsername.addEventListener('input', function() {
        document.getElementById('cardUsername').value = this.value;
        document.getElementById('transferUsername').value = this.value;
    });
}

function updateUserDisplay() {
    // Update avatar and username display
    const avatarImg = userAvatar.querySelector('img');
    if (avatarImg) {
        avatarImg.src = currentUser.avatar;
    }
}

async function handleUsernameCheck() {
    const username = tiktokUsername.value.trim();

    if (!username) {
        showUsernameResult('Vui lòng nhập username TikTok', 'error');
        return;
    }

    // Show loading
    checkUsername.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Kiểm tra...';
    checkUsername.disabled = true;

    try {
        const response = await fetch('api/endpoints/verify-username.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username: username })
        });

        const result = await response.json();

        if (result.success) {
            const account = result.data;
            let message = `✓ Tài khoản @${account.username} hợp lệ`;
            if (account.is_verified) {
                message += ' ✓';
            }
            if (account.follower_count > 0) {
                message += ` (${account.follower_count.toLocaleString()} followers)`;
            }

            showUsernameResult(message, 'success');
            updateSummary();
        } else {
            showUsernameResult('❌ ' + result.message, 'error');
        }
    } catch (error) {
        console.error('Error verifying username:', error);
        console.log('Falling back to mock verification...');

        // Fallback to mock verification
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Mock validation - basic checks
        const isValid = username.length >= 3 && !username.includes(' ') && /^[a-zA-Z0-9_.]+$/.test(username);

        if (isValid) {
            const mockFollowers = Math.floor(Math.random() * 100000) + 1000;
            showUsernameResult(`✓ Tài khoản @${username} hợp lệ (${mockFollowers.toLocaleString()} followers) [DEMO MODE]`, 'success');
            updateSummary();
        } else {
            showUsernameResult('❌ Username không hợp lệ hoặc không tồn tại [DEMO MODE]', 'error');
        }
    } finally {
        checkUsername.innerHTML = 'Kiểm tra';
        checkUsername.disabled = false;
    }
}

function showUsernameResult(message, type) {
    usernameResult.textContent = message;
    usernameResult.className = `username-result ${type}`;
}

async function loadPackages() {
    try {
        // Check if we can access the API
        const response = await fetch('api/endpoints/packages.php');
        const result = await response.json();

        if (result.success) {
            displayPackages(result.data);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('Error loading packages:', error);
        console.log('Falling back to mock data...');

        // Show demo notice
        showDemoNotice();

        // Fallback to mock data for testing
        const mockPackages = [
            { id: 1, name: 'Gói Starter', coins: 100, price: 25000, discount: 0, formatted_price: '25,000 VNĐ', formatted_coins: '100 xu' },
            { id: 2, name: 'Gói Basic', coins: 330, price: 75000, discount: 0, formatted_price: '75,000 VNĐ', formatted_coins: '330 xu' },
            { id: 3, name: 'Gói Popular', coins: 660, price: 150000, discount: 0, formatted_price: '150,000 VNĐ', formatted_coins: '660 xu' },
            { id: 4, name: 'Gói Premium', coins: 1320, price: 300000, discount: 0, formatted_price: '300,000 VNĐ', formatted_coins: '1,320 xu' },
            { id: 5, name: 'Gói VIP', coins: 3300, price: 750000, discount: 0, formatted_price: '750,000 VNĐ', formatted_coins: '3,300 xu' },
            { id: 6, name: 'Gói Ultimate', coins: 6600, price: 1500000, discount: 0, formatted_price: '1,500,000 VNĐ', formatted_coins: '6,600 xu' }
        ];

        // Simulate loading delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        displayPackages(mockPackages);
    }
}

function displayPackages(packages) {
    packageLoading.style.display = 'none';
    packageGrid.style.display = 'grid';

    packageGrid.innerHTML = packages.map(pkg => `
        <div class="package-item" data-package-id="${pkg.id}" data-coins="${pkg.coins}" data-price="${pkg.price}">
            <div class="package-coins">${pkg.formatted_coins}</div>
            <div class="package-price">${pkg.formatted_price}</div>
        </div>
    `).join('');

    // Add click events to package items
    document.querySelectorAll('.package-item').forEach(item => {
        item.addEventListener('click', function() {
            selectPackage(this);
        });
    });
}

function selectPackage(packageElement) {
    // Remove previous selection
    document.querySelectorAll('.package-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Select current package
    packageElement.classList.add('selected');

    selectedPackage = {
        id: packageElement.dataset.packageId,
        coins: parseInt(packageElement.dataset.coins),
        price: parseInt(packageElement.dataset.price)
    };

    updateSummary();
    updateTransferAmount();
}

function switchPaymentTab(tabName) {
    // Update tab buttons
    tabBtns.forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.tab === tabName) {
            btn.classList.add('active');
        }
    });

    // Update payment forms
    paymentForms.forEach(form => {
        form.classList.remove('active');
        if (form.id === `${tabName}Payment`) {
            form.classList.add('active');
        }
    });
}

function selectProvider(provider) {
    // Remove previous selection
    providerItems.forEach(item => {
        item.classList.remove('selected');
    });

    // Select current provider
    const selectedItem = document.querySelector(`[data-provider="${provider}"]`);
    if (selectedItem) {
        selectedItem.classList.add('selected');
        selectedProvider = provider;
    }
}

function updateSummary() {
    const username = tiktokUsername.value.trim();

    if (username && selectedPackage) {
        summaryContent.style.display = 'none';
        summaryDetails.style.display = 'block';

        document.getElementById('summaryUsername').textContent = `@${username}`;
        document.getElementById('summaryPackage').textContent = `${selectedPackage.coins.toLocaleString()} xu`;
        document.getElementById('summaryPrice').textContent = `${selectedPackage.price.toLocaleString()} VNĐ`;
    } else {
        summaryContent.style.display = 'block';
        summaryDetails.style.display = 'none';
    }
}

function updateTransferAmount() {
    const transferAmount = document.getElementById('transferAmount');
    if (selectedPackage) {
        transferAmount.textContent = `${selectedPackage.price.toLocaleString()} VNĐ`;
    } else {
        transferAmount.textContent = '0 VNĐ';
    }
}

async function handleCardPayment() {
    const cardValue = document.getElementById('cardValue').value;
    const cardSerial = document.getElementById('cardSerial').value.trim();
    const cardCode = document.getElementById('cardCode').value.trim();
    const username = document.getElementById('cardUsername').value.trim();

    // Validation
    if (!selectedProvider) {
        alert('Vui lòng chọn nhà mạng');
        return;
    }

    if (!cardValue || !cardSerial || !cardCode || !username) {
        alert('Vui lòng điền đầy đủ thông tin');
        return;
    }

    if (!selectedPackage) {
        alert('Vui lòng chọn gói xu');
        return;
    }

    // Show loading
    const submitBtn = document.getElementById('submitCard');
    const originalText = submitBtn.textContent;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang xử lý...';
    submitBtn.disabled = true;

    try {
        const response = await fetch('api/endpoints/transactions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                tiktok_username: username,
                package_id: selectedPackage.id,
                payment_method: 'card',
                payment_data: {
                    provider: selectedProvider,
                    card_value: parseFloat(cardValue),
                    serial_number: cardSerial,
                    card_code: cardCode
                }
            })
        });

        const result = await response.json();

        if (result.success) {
            const transaction = {
                id: result.data.transaction_id,
                username: username,
                package: `${selectedPackage.coins} xu`,
                method: `Thẻ cào ${selectedProvider}`,
                status: 'Đang xử lý',
                time: new Date().toLocaleString('vi-VN')
            };

            addTransaction(transaction);
            alert(`Giao dịch đã được tạo!\nMã giao dịch: ${result.data.transaction_code}\nTrạng thái: ${result.data.status}`);

            // Reset form
            resetCardForm();
        } else {
            alert('Lỗi: ' + result.message);
        }

    } catch (error) {
        console.error('Error processing card payment:', error);
        console.log('Falling back to mock payment...');

        // Fallback to mock payment for demo
        await new Promise(resolve => setTimeout(resolve, 2000));

        const transaction = {
            id: Date.now(),
            username: username,
            package: `${selectedPackage.coins} xu`,
            method: `Thẻ cào ${selectedProvider}`,
            status: 'Thành công [DEMO]',
            time: new Date().toLocaleString('vi-VN')
        };

        addTransaction(transaction);
        alert(`Demo: Giao dịch thành công!\nMã giao dịch: TXN${Date.now()}\nTrạng thái: Hoàn thành`);

        // Reset form
        resetCardForm();
    } finally {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

async function handleTransferInfo() {
    const username = document.getElementById('transferUsername').value.trim();

    if (!username) {
        alert('Vui lòng nhập username TikTok');
        return;
    }

    if (!selectedPackage) {
        alert('Vui lòng chọn gói xu');
        return;
    }

    // Show loading
    const btn = document.getElementById('getTransferInfo');
    const originalText = btn.textContent;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Đang tạo...';
    btn.disabled = true;

    try {
        const response = await fetch('api/endpoints/transactions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                tiktok_username: username,
                package_id: selectedPackage.id,
                payment_method: 'bank_transfer',
                payment_data: {
                    username: username
                }
            })
        });

        const result = await response.json();

        if (result.success) {
            const paymentInfo = result.data.payment_info;
            showTransferInfo(paymentInfo);

            // Add transaction to history
            const transaction = {
                id: result.data.transaction_id,
                username: username,
                package: `${selectedPackage.coins} xu`,
                method: 'Chuyển khoản',
                status: 'Chờ thanh toán',
                time: new Date().toLocaleString('vi-VN')
            };
            addTransaction(transaction);
        } else {
            alert('Lỗi: ' + result.message);
        }

    } catch (error) {
        console.error('Error getting transfer info:', error);
        console.log('Falling back to mock transfer info...');

        // Fallback to mock transfer info
        await new Promise(resolve => setTimeout(resolve, 1000));

        const mockTransferInfo = {
            bank_name: 'Vietcombank',
            account_number: '**********',
            account_name: 'NGUYEN VAN A',
            amount: selectedPackage.price,
            formatted_amount: selectedPackage.price.toLocaleString() + ' VNĐ',
            transfer_content: `NAP ${username} ${Date.now().toString().slice(-4)}`
        };

        showTransferInfo(mockTransferInfo);

        // Add transaction to history
        const transaction = {
            id: Date.now(),
            username: username,
            package: `${selectedPackage.coins} xu`,
            method: 'Chuyển khoản [DEMO]',
            status: 'Chờ thanh toán',
            time: new Date().toLocaleString('vi-VN')
        };
        addTransaction(transaction);
    } finally {
        btn.textContent = originalText;
        btn.disabled = false;
    }
}

function showTransferInfo(info) {
    const message = `
Thông tin chuyển khoản:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🏦 Ngân hàng: ${info.bank_name}
💳 Số tài khoản: ${info.account_number}
👤 Chủ tài khoản: ${info.account_name}
💰 Số tiền: ${info.formatted_amount}
📝 Nội dung: ${info.transfer_content}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

⚠️ Lưu ý: Chuyển khoản đúng nội dung để được xử lý tự động
    `;

    alert(message);
}

function addTransaction(transaction) {
    currentUser.transactions.unshift(transaction);
    updateTransactionHistory();
}

function updateTransactionHistory() {
    const noTransactions = document.getElementById('noTransactions');
    const transactionTable = document.getElementById('transactionTable');
    const transactionBody = document.getElementById('transactionBody');

    if (currentUser.transactions.length === 0) {
        noTransactions.style.display = 'block';
        transactionTable.style.display = 'none';
    } else {
        noTransactions.style.display = 'none';
        transactionTable.style.display = 'table';

        transactionBody.innerHTML = currentUser.transactions.map(transaction => `
            <tr>
                <td>${transaction.time}</td>
                <td>@${transaction.username}</td>
                <td>${transaction.package}</td>
                <td>${transaction.method}</td>
                <td><span class="status-success">${transaction.status}</span></td>
            </tr>
        `).join('');
    }
}

function resetCardForm() {
    document.getElementById('cardValue').value = '';
    document.getElementById('cardSerial').value = '';
    document.getElementById('cardCode').value = '';

    // Reset provider selection
    providerItems.forEach(item => {
        item.classList.remove('selected');
    });
    selectedProvider = null;
}

function showAccountModal() {
    accountModal.classList.add('show');

    // Load account info
    const accountInfo = document.querySelector('.account-info');
    accountInfo.innerHTML = `
        <div class="account-details">
            <p><strong>Username:</strong> ${currentUser.username}</p>
            <p><strong>Số dư:</strong> ${currentUser.balance.toLocaleString()} VNĐ</p>
            <p><strong>Tổng giao dịch:</strong> ${currentUser.transactions.length}</p>
            <p><strong>Ngày tham gia:</strong> ${new Date().toLocaleDateString('vi-VN')}</p>
        </div>
    `;
}

function hideAccountModal() {
    accountModal.classList.remove('show');
}

// Close modal when clicking outside
accountModal.addEventListener('click', function(e) {
    if (e.target === accountModal) {
        hideAccountModal();
    }
});

// Logout functionality
document.getElementById('logout').addEventListener('click', function(e) {
    e.preventDefault();
    if (confirm('Bạn có chắc chắn muốn đăng xuất?')) {
        // Reset user data
        currentUser = {
            username: 'User',
            avatar: 'https://api.dicebear.com/8.x/initials/svg?seed=User',
            balance: 0,
            transactions: []
        };

        // Reset forms
        document.getElementById('tiktokUsername').value = '';
        document.getElementById('cardUsername').value = '';
        document.getElementById('transferUsername').value = '';
        usernameResult.textContent = '';

        // Reset selections
        selectedPackage = null;
        selectedProvider = null;

        // Update UI
        updateUserDisplay();
        updateSummary();
        updateTransactionHistory();

        alert('Đã đăng xuất thành công!');
    }
});

// Transaction history link
document.getElementById('transactionHistory').addEventListener('click', function(e) {
    e.preventDefault();
    document.getElementById('historySection').scrollIntoView({ behavior: 'smooth' });
    dropdownMenu.classList.remove('show');
});
