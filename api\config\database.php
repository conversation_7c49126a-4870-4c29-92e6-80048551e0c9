<?php
/**
 * Database Configuration
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'tiktok_recharge';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    private $conn;

    public function __construct() {
        $this->connect();
    }

    private function connect() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $e) {
            error_log("Connection Error: " . $e->getMessage());
            throw new Exception("Database connection failed");
        }
    }

    public function getConnection() {
        return $this->conn;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->conn->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            error_log("Query Error: " . $e->getMessage());
            throw new Exception("Database query failed");
        }
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }

    public function fetchOne($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->conn->lastInsertId();
    }

    public function update($table, $data, $where, $whereParams = []) {
        $setClause = [];
        foreach (array_keys($data) as $key) {
            $setClause[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $setClause);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }

    public function beginTransaction() {
        return $this->conn->beginTransaction();
    }

    public function commit() {
        return $this->conn->commit();
    }

    public function rollback() {
        return $this->conn->rollback();
    }
}

/**
 * Response Helper Class
 */
class ApiResponse {
    public static function success($data = null, $message = 'Success', $code = 200) {
        http_response_code($code);
        header('Content-Type: application/json');
        
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public static function error($message = 'Error', $code = 400, $data = null) {
        http_response_code($code);
        header('Content-Type: application/json');
        
        $response = [
            'success' => false,
            'message' => $message,
            'data' => $data
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }

    public static function validation($errors) {
        self::error('Validation failed', 422, $errors);
    }
}

/**
 * Utility Functions
 */
class Utils {
    public static function generateTransactionCode() {
        return 'TXN' . date('YmdHis') . rand(1000, 9999);
    }

    public static function validateTikTokUsername($username) {
        // Basic validation - in real app, you might call TikTok API
        if (empty($username) || strlen($username) < 3 || strlen($username) > 50) {
            return false;
        }
        
        // Check for valid characters (alphanumeric, underscore, dot)
        if (!preg_match('/^[a-zA-Z0-9_.]+$/', $username)) {
            return false;
        }
        
        return true;
    }

    public static function formatCurrency($amount) {
        return number_format($amount, 0, ',', '.') . ' VNĐ';
    }

    public static function sanitizeInput($input) {
        return htmlspecialchars(strip_tags(trim($input)));
    }

    public static function validateCardSerial($serial, $provider) {
        // Basic validation - in real app, you would validate against provider's format
        $patterns = [
            'viettel' => '/^\d{11,15}$/',
            'vinaphone' => '/^\d{11,15}$/',
            'mobifone' => '/^\d{11,15}$/',
            'zing' => '/^\d{12,16}$/',
            'garena' => '/^\d{12,16}$/'
        ];
        
        if (!isset($patterns[$provider])) {
            return false;
        }
        
        return preg_match($patterns[$provider], $serial);
    }

    public static function validateCardCode($code, $provider) {
        // Basic validation - in real app, you would validate against provider's format
        $patterns = [
            'viettel' => '/^\d{13,15}$/',
            'vinaphone' => '/^\d{13,15}$/',
            'mobifone' => '/^\d{13,15}$/',
            'zing' => '/^[A-Z0-9]{12,16}$/',
            'garena' => '/^[A-Z0-9]{12,16}$/'
        ];
        
        if (!isset($patterns[$provider])) {
            return false;
        }
        
        return preg_match($patterns[$provider], $code);
    }

    public static function logActivity($action, $data = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'action' => $action,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'data' => $data
        ];
        
        error_log(json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
}

/**
 * CORS and Security Headers
 */
function setCorsHeaders() {
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
}

/**
 * Rate Limiting (Simple implementation)
 */
class RateLimit {
    private static $requests = [];
    
    public static function check($ip, $limit = 60, $window = 3600) {
        $now = time();
        $windowStart = $now - $window;
        
        // Clean old requests
        if (isset(self::$requests[$ip])) {
            self::$requests[$ip] = array_filter(self::$requests[$ip], function($timestamp) use ($windowStart) {
                return $timestamp > $windowStart;
            });
        } else {
            self::$requests[$ip] = [];
        }
        
        // Check limit
        if (count(self::$requests[$ip]) >= $limit) {
            return false;
        }
        
        // Add current request
        self::$requests[$ip][] = $now;
        return true;
    }
}

// Set timezone
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Enable error reporting for development
if (getenv('APP_ENV') === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}
?>
