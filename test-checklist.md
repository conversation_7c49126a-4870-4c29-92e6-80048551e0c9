# 🧪 Test Checklist - TikTok Recharge System

## ✅ Checklist kiểm tra chức năng

### 1. Giao diện tổng thể
- [ ] Trang load được không lỗi
- [ ] Logo TikTok hiển thị đúng
- [ ] Header với dropdown menu hoạt động
- [ ] Layout responsive trên mobile
- [ ] Gradient background hiển thị đẹp
- [ ] Demo notice hiển thị khi chạy offline

### 2. Form nhập Username TikTok
- [ ] Input field hoạt động bình thường
- [ ] Nút "Kiểm tra" có loading state
- [ ] Validation username c<PERSON> bản (>= 3 ký tự, không có space)
- [ ] Hiển thị kết quả kiểm tra (success/error)
- [ ] Auto-fill username vào các form khác

### 3. Chọn gói xu
- [ ] Hiển thị 6 gói xu với giá khác nhau
- [ ] Click chọn gói hoạt động (highlight selected)
- [ ] Thông tin gói cập nhật vào sidebar
- [ ] Format số tiền và xu đúng

### 4. <PERSON><PERSON><PERSON><PERSON> thức thanh toán
- [ ] Tab switcher giữa "Thẻ cào" và "Chuyển khoản"
- [ ] Form thẻ cào:
  - [ ] Chọn nhà mạng (5 options)
  - [ ] Dropdown mệnh giá thẻ
  - [ ] Input số seri và mã thẻ
  - [ ] Validation form đầy đủ
  - [ ] Submit button với loading state
- [ ] Form chuyển khoản:
  - [ ] Hiển thị số tiền cần chuyển
  - [ ] Nút lấy thông tin chuyển khoản
  - [ ] Modal/alert hiển thị thông tin bank

### 5. Sidebar tóm tắt đơn hàng
- [ ] Hiển thị placeholder khi chưa chọn
- [ ] Cập nhật thông tin khi chọn username + gói
- [ ] Format hiển thị đúng
- [ ] Sticky position hoạt động

### 6. Lịch sử giao dịch
- [ ] Hiển thị "Chưa có giao dịch" ban đầu
- [ ] Thêm giao dịch mới khi thanh toán
- [ ] Format thời gian đúng
- [ ] Hiển thị trạng thái giao dịch

### 7. User menu
- [ ] Click avatar hiển thị dropdown
- [ ] Click outside đóng dropdown
- [ ] Menu "Tài khoản" mở modal
- [ ] Menu "Lịch sử nạp" scroll đến section
- [ ] Menu "Đăng xuất" reset form

### 8. Modal thông tin tài khoản
- [ ] Mở/đóng modal hoạt động
- [ ] Hiển thị thông tin user
- [ ] Click outside đóng modal
- [ ] Nút X đóng modal

### 9. Responsive design
- [ ] Mobile: sidebar chuyển lên trên
- [ ] Mobile: form stack vertical
- [ ] Mobile: provider grid 2-3 columns
- [ ] Tablet: layout phù hợp
- [ ] Desktop: layout 2 columns

### 10. Demo mode features
- [ ] Demo notice hiển thị khi offline
- [ ] Mock data packages load được
- [ ] Mock username verification
- [ ] Mock payment processing
- [ ] Thông báo "[DEMO]" trong kết quả

## 🐛 Các lỗi đã biết và cách fix

### Lỗi 1: API không hoạt động
**Nguyên nhân**: Chạy file HTML trực tiếp, không có PHP server
**Giải pháp**: Đã thêm fallback mock data

### Lỗi 2: CORS errors
**Nguyên nhân**: Browser block cross-origin requests
**Giải pháp**: Cần chạy qua web server hoặc dùng mock data

### Lỗi 3: Database chưa setup
**Nguyên nhân**: MySQL chưa được cài đặt/cấu hình
**Giải pháp**: Xem hướng dẫn trong README.md

## 🚀 Hướng dẫn test đầy đủ

### Test với mock data (hiện tại):
1. Mở `file:///d:/Project/ScamTiktok/index.html`
2. Thực hiện checklist trên
3. Tất cả sẽ hoạt động với dữ liệu mô phỏng

### Test với PHP server:
1. Cài đặt XAMPP/WAMP/LAMP
2. Copy project vào htdocs/www
3. Import database từ `database/setup.sql`
4. Cấu hình `api/config/database.php`
5. Truy cập `http://localhost/ScamTiktok/`

### Test API riêng lẻ:
1. Mở `test.html` trong browser
2. Click các nút test API
3. Xem kết quả JSON trong console

## 📊 Kết quả test mong đợi

### ✅ Thành công:
- Giao diện load đẹp, responsive
- Tất cả form hoạt động
- Validation đúng
- Mock data hiển thị
- Demo mode thông báo rõ ràng

### ⚠️ Cần cải thiện:
- Cần setup PHP server để test API thật
- Cần thêm hình ảnh logo nhà mạng
- Cần tối ưu performance
- Cần thêm unit tests

## 🔧 Debug tips

### Mở Developer Tools:
- F12 hoặc Ctrl+Shift+I
- Tab Console: xem lỗi JavaScript
- Tab Network: xem API calls
- Tab Elements: inspect HTML/CSS

### Common issues:
- Nếu packages không load: Check console errors
- Nếu styling bị lỗi: Check CSS path
- Nếu JavaScript không chạy: Check syntax errors
- Nếu responsive không đúng: Check viewport meta tag
