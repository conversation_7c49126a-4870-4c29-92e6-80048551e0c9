-- Database setup for TikTok Coin Recharge System
-- Tạo database
CREATE DATABASE IF NOT EXISTS tiktok_recharge CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tiktok_recharge;

-- Bảng users - <PERSON><PERSON><PERSON><PERSON> lý người dùng
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    balance DECIMAL(15,2) DEFAULT 0.00,
    avatar_url VARCHAR(255),
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bảng tiktok_accounts - <PERSON><PERSON><PERSON> thông tin tài khoản TikTok đã verify
CREATE TABLE tiktok_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100),
    follower_count INT DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'not_found') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng coin_packages - Các gói xu có sẵn
CREATE TABLE coin_packages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    coin_amount INT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bảng payment_methods - Phương thức thanh toán
CREATE TABLE payment_methods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    type ENUM('card', 'bank_transfer', 'ewallet') NOT NULL,
    provider VARCHAR(50), -- viettel, vinaphone, mobifone, zing, garena cho thẻ cào
    is_active BOOLEAN DEFAULT TRUE,
    fee_percent DECIMAL(5,2) DEFAULT 0.00,
    min_amount DECIMAL(10,2) DEFAULT 0.00,
    max_amount DECIMAL(10,2) DEFAULT *********.99,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bảng transactions - Lịch sử giao dịch
CREATE TABLE transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    tiktok_username VARCHAR(50) NOT NULL,
    package_id INT,
    payment_method_id INT,
    transaction_code VARCHAR(50) UNIQUE NOT NULL,
    coin_amount INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    fee DECIMAL(10,2) DEFAULT 0.00,
    final_amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    payment_data JSON, -- Lưu thông tin thanh toán (serial, code, bank info, etc.)
    notes TEXT,
    processed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (package_id) REFERENCES coin_packages(id) ON DELETE SET NULL,
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id) ON DELETE SET NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_tiktok_username (tiktok_username),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Bảng card_transactions - Chi tiết giao dịch thẻ cào
CREATE TABLE card_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL,
    provider VARCHAR(20) NOT NULL, -- viettel, vinaphone, mobifone, zing, garena
    card_value DECIMAL(10,2) NOT NULL,
    serial_number VARCHAR(50) NOT NULL,
    card_code VARCHAR(50) NOT NULL,
    verification_status ENUM('pending', 'valid', 'invalid', 'used') DEFAULT 'pending',
    verification_response TEXT,
    verified_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_serial_number (serial_number)
);

-- Bảng bank_transfers - Chi tiết giao dịch chuyển khoản
CREATE TABLE bank_transfers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    transaction_id INT NOT NULL,
    bank_name VARCHAR(100) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    transfer_content VARCHAR(255) NOT NULL,
    expected_amount DECIMAL(10,2) NOT NULL,
    received_amount DECIMAL(10,2) DEFAULT 0.00,
    transfer_time TIMESTAMP NULL,
    verification_status ENUM('pending', 'verified', 'mismatch') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (transaction_id) REFERENCES transactions(id) ON DELETE CASCADE,
    INDEX idx_transaction_id (transaction_id)
);

-- Bảng admin_users - Quản trị viên
CREATE TABLE admin_users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'moderator') DEFAULT 'moderator',
    permissions JSON,
    last_login TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bảng system_settings - Cài đặt hệ thống
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert dữ liệu mẫu

-- Thêm gói xu mẫu
INSERT INTO coin_packages (name, coin_amount, price, sort_order) VALUES
('Gói Starter', 100, 25000, 1),
('Gói Basic', 330, 75000, 2),
('Gói Popular', 660, 150000, 3),
('Gói Premium', 1320, 300000, 4),
('Gói VIP', 3300, 750000, 5),
('Gói Ultimate', 6600, 1500000, 6);

-- Thêm phương thức thanh toán
INSERT INTO payment_methods (name, type, provider, fee_percent) VALUES
('Thẻ cào Viettel', 'card', 'viettel', 5.00),
('Thẻ cào VinaPhone', 'card', 'vinaphone', 5.00),
('Thẻ cào Mobifone', 'card', 'mobifone', 5.00),
('Thẻ cào Zing', 'card', 'zing', 8.00),
('Thẻ cào Garena', 'card', 'garena', 8.00),
('Chuyển khoản ngân hàng', 'bank_transfer', NULL, 0.00);

-- Thêm cài đặt hệ thống
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('site_name', 'Nạp Xu TikTok - Giá Rẻ', 'Tên website'),
('maintenance_mode', 'false', 'Chế độ bảo trì'),
('min_recharge_amount', '10000', 'Số tiền nạp tối thiểu'),
('max_recharge_amount', '5000000', 'Số tiền nạp tối đa'),
('bank_account_number', '**********', 'Số tài khoản ngân hàng'),
('bank_account_name', 'NGUYEN VAN A', 'Tên chủ tài khoản'),
('bank_name', 'Vietcombank', 'Tên ngân hàng'),
('auto_process_cards', 'true', 'Tự động xử lý thẻ cào'),
('telegram_bot_token', '', 'Token bot Telegram thông báo'),
('telegram_chat_id', '', 'Chat ID Telegram nhận thông báo');

-- Tạo user admin mặc định (password: admin123)
INSERT INTO admin_users (username, email, password_hash, full_name, role) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', 'admin');

-- Tạo indexes để tối ưu performance
CREATE INDEX idx_transactions_status_created ON transactions(status, created_at);
CREATE INDEX idx_transactions_tiktok_user ON transactions(tiktok_username, created_at);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);

-- Views để báo cáo
CREATE VIEW transaction_summary AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_transactions,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_transactions,
    SUM(CASE WHEN status = 'completed' THEN final_amount ELSE 0 END) as total_revenue,
    SUM(CASE WHEN status = 'completed' THEN coin_amount ELSE 0 END) as total_coins_sold
FROM transactions 
GROUP BY DATE(created_at)
ORDER BY date DESC;

CREATE VIEW popular_packages AS
SELECT 
    cp.name,
    cp.coin_amount,
    cp.price,
    COUNT(t.id) as purchase_count,
    SUM(CASE WHEN t.status = 'completed' THEN t.final_amount ELSE 0 END) as total_revenue
FROM coin_packages cp
LEFT JOIN transactions t ON cp.id = t.package_id
GROUP BY cp.id
ORDER BY purchase_count DESC;
