<?php
/**
 * TikTok Username Verification API
 * Verifies if a TikTok username exists and is valid
 */

require_once '../config/database.php';

setCorsHeaders();

// Rate limiting
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!RateLimit::check($clientIp, 30, 3600)) {
    ApiResponse::error('Too many requests', 429);
}

$database = new Database();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            verifyUsername($database);
            break;
        
        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    Utils::logActivity('verify_username_error', ['error' => $e->getMessage()]);
    ApiResponse::error('Internal server error', 500);
}

function verifyUsername($database) {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['username'])) {
        ApiResponse::error('Username is required', 400);
    }
    
    $username = Utils::sanitizeInput($input['username']);
    
    // Basic validation
    if (!Utils::validateTikTokUsername($username)) {
        ApiResponse::error('Invalid username format', 400);
    }
    
    try {
        // Check if username exists in our cache
        $cachedAccount = $database->fetchOne(
            "SELECT * FROM tiktok_accounts WHERE username = ? AND last_checked > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
            [$username]
        );
        
        if ($cachedAccount) {
            $response = [
                'username' => $cachedAccount['username'],
                'display_name' => $cachedAccount['display_name'],
                'is_verified' => (bool)$cachedAccount['is_verified'],
                'follower_count' => (int)$cachedAccount['follower_count'],
                'status' => $cachedAccount['status'],
                'cached' => true
            ];
            
            Utils::logActivity('username_verified_cached', ['username' => $username]);
            ApiResponse::success($response, 'Username verified (cached)');
        }
        
        // Simulate TikTok API call (in real implementation, you would call actual TikTok API)
        $verificationResult = simulateTikTokVerification($username);
        
        if ($verificationResult['exists']) {
            // Save/update in cache
            $accountData = [
                'username' => $username,
                'display_name' => $verificationResult['display_name'],
                'follower_count' => $verificationResult['follower_count'],
                'is_verified' => $verificationResult['is_verified'],
                'status' => 'active',
                'last_checked' => date('Y-m-d H:i:s')
            ];
            
            // Check if account exists
            $existingAccount = $database->fetchOne(
                "SELECT id FROM tiktok_accounts WHERE username = ?",
                [$username]
            );
            
            if ($existingAccount) {
                $database->update(
                    'tiktok_accounts',
                    $accountData,
                    'username = ?',
                    [$username]
                );
            } else {
                $database->insert('tiktok_accounts', $accountData);
            }
            
            $response = [
                'username' => $username,
                'display_name' => $verificationResult['display_name'],
                'is_verified' => $verificationResult['is_verified'],
                'follower_count' => $verificationResult['follower_count'],
                'status' => 'active',
                'cached' => false
            ];
            
            Utils::logActivity('username_verified_new', ['username' => $username]);
            ApiResponse::success($response, 'Username verified successfully');
            
        } else {
            // Mark as not found
            $accountData = [
                'username' => $username,
                'display_name' => null,
                'follower_count' => 0,
                'is_verified' => false,
                'status' => 'not_found',
                'last_checked' => date('Y-m-d H:i:s')
            ];
            
            $existingAccount = $database->fetchOne(
                "SELECT id FROM tiktok_accounts WHERE username = ?",
                [$username]
            );
            
            if ($existingAccount) {
                $database->update(
                    'tiktok_accounts',
                    $accountData,
                    'username = ?',
                    [$username]
                );
            } else {
                $database->insert('tiktok_accounts', $accountData);
            }
            
            Utils::logActivity('username_not_found', ['username' => $username]);
            ApiResponse::error('Username not found or invalid', 404);
        }
        
    } catch (Exception $e) {
        throw $e;
    }
}

function simulateTikTokVerification($username) {
    // Simulate API delay
    usleep(500000); // 0.5 seconds
    
    // Mock verification logic
    $exists = true;
    
    // Some usernames that should fail
    $invalidUsernames = ['test', 'admin', 'tiktok', 'fake', 'spam'];
    if (in_array(strtolower($username), $invalidUsernames)) {
        $exists = false;
    }
    
    // Username too short
    if (strlen($username) < 3) {
        $exists = false;
    }
    
    // Contains invalid characters
    if (!preg_match('/^[a-zA-Z0-9_.]+$/', $username)) {
        $exists = false;
    }
    
    if ($exists) {
        return [
            'exists' => true,
            'display_name' => '@' . $username,
            'follower_count' => rand(100, 1000000),
            'is_verified' => rand(0, 10) > 8 // 20% chance of being verified
        ];
    } else {
        return [
            'exists' => false,
            'display_name' => null,
            'follower_count' => 0,
            'is_verified' => false
        ];
    }
}
?>
