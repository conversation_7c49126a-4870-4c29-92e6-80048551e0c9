<?php
/**
 * Packages API Endpoint
 * Handles coin package related operations
 */

require_once '../config/database.php';

setCorsHeaders();

// Rate limiting
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!RateLimit::check($clientIp, 100, 3600)) {
    ApiResponse::error('Too many requests', 429);
}

$database = new Database();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            getPackages($database);
            break;
        
        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    Utils::logActivity('packages_error', ['error' => $e->getMessage()]);
    ApiResponse::error('Internal server error', 500);
}

function getPackages($database) {
    try {
        $sql = "SELECT id, name, coin_amount, price, discount_percent, sort_order 
                FROM coin_packages 
                WHERE is_active = 1 
                ORDER BY sort_order ASC, coin_amount ASC";
        
        $packages = $database->fetchAll($sql);
        
        // Format packages for frontend
        $formattedPackages = array_map(function($package) {
            return [
                'id' => (int)$package['id'],
                'name' => $package['name'],
                'coins' => (int)$package['coin_amount'],
                'price' => (float)$package['price'],
                'discount' => (float)$package['discount_percent'],
                'formatted_price' => Utils::formatCurrency($package['price']),
                'formatted_coins' => number_format($package['coin_amount']) . ' xu'
            ];
        }, $packages);
        
        Utils::logActivity('packages_fetched', ['count' => count($formattedPackages)]);
        
        ApiResponse::success($formattedPackages, 'Packages loaded successfully');
        
    } catch (Exception $e) {
        throw $e;
    }
}
?>
