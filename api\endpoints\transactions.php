<?php
/**
 * Transactions API Endpoint
 * Handles transaction creation and management
 */

require_once '../config/database.php';

setCorsHeaders();

// Rate limiting
$clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
if (!RateLimit::check($clientIp, 50, 3600)) {
    ApiResponse::error('Too many requests', 429);
}

$database = new Database();
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            createTransaction($database);
            break;
        
        case 'GET':
            getTransactions($database);
            break;
        
        default:
            ApiResponse::error('Method not allowed', 405);
    }
} catch (Exception $e) {
    Utils::logActivity('transaction_error', ['error' => $e->getMessage()]);
    ApiResponse::error('Internal server error', 500);
}

function createTransaction($database) {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    
    // Validate required fields
    $requiredFields = ['tiktok_username', 'package_id', 'payment_method', 'payment_data'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty($input[$field])) {
            ApiResponse::error("Field {$field} is required", 400);
        }
    }
    
    $tiktokUsername = Utils::sanitizeInput($input['tiktok_username']);
    $packageId = (int)$input['package_id'];
    $paymentMethod = Utils::sanitizeInput($input['payment_method']);
    $paymentData = $input['payment_data'];
    
    // Validate TikTok username
    if (!Utils::validateTikTokUsername($tiktokUsername)) {
        ApiResponse::error('Invalid TikTok username', 400);
    }
    
    try {
        $database->beginTransaction();
        
        // Get package details
        $package = $database->fetchOne(
            "SELECT * FROM coin_packages WHERE id = ? AND is_active = 1",
            [$packageId]
        );
        
        if (!$package) {
            $database->rollback();
            ApiResponse::error('Invalid package', 400);
        }
        
        // Get payment method details
        $paymentMethodData = $database->fetchOne(
            "SELECT * FROM payment_methods WHERE type = ? AND is_active = 1",
            [$paymentMethod]
        );
        
        if (!$paymentMethodData) {
            $database->rollback();
            ApiResponse::error('Invalid payment method', 400);
        }
        
        // Calculate fees and final amount
        $amount = $package['price'];
        $fee = $amount * ($paymentMethodData['fee_percent'] / 100);
        $finalAmount = $amount + $fee;
        
        // Generate transaction code
        $transactionCode = Utils::generateTransactionCode();
        
        // Create transaction
        $transactionData = [
            'tiktok_username' => $tiktokUsername,
            'package_id' => $packageId,
            'payment_method_id' => $paymentMethodData['id'],
            'transaction_code' => $transactionCode,
            'coin_amount' => $package['coin_amount'],
            'amount' => $amount,
            'fee' => $fee,
            'final_amount' => $finalAmount,
            'status' => 'pending',
            'payment_data' => json_encode($paymentData)
        ];
        
        $transactionId = $database->insert('transactions', $transactionData);
        
        // Handle specific payment method
        if ($paymentMethod === 'card') {
            $result = handleCardPayment($database, $transactionId, $paymentData);
        } elseif ($paymentMethod === 'bank_transfer') {
            $result = handleBankTransfer($database, $transactionId, $paymentData, $finalAmount);
        } else {
            $database->rollback();
            ApiResponse::error('Unsupported payment method', 400);
        }
        
        $database->commit();
        
        Utils::logActivity('transaction_created', [
            'transaction_id' => $transactionId,
            'username' => $tiktokUsername,
            'amount' => $finalAmount,
            'method' => $paymentMethod
        ]);
        
        ApiResponse::success([
            'transaction_id' => $transactionId,
            'transaction_code' => $transactionCode,
            'amount' => $finalAmount,
            'status' => 'pending',
            'payment_info' => $result
        ], 'Transaction created successfully');
        
    } catch (Exception $e) {
        $database->rollback();
        throw $e;
    }
}

function handleCardPayment($database, $transactionId, $paymentData) {
    // Validate card data
    $requiredFields = ['provider', 'card_value', 'serial_number', 'card_code'];
    foreach ($requiredFields as $field) {
        if (!isset($paymentData[$field]) || empty($paymentData[$field])) {
            throw new Exception("Card field {$field} is required");
        }
    }
    
    $provider = Utils::sanitizeInput($paymentData['provider']);
    $cardValue = (float)$paymentData['card_value'];
    $serialNumber = Utils::sanitizeInput($paymentData['serial_number']);
    $cardCode = Utils::sanitizeInput($paymentData['card_code']);
    
    // Validate card format
    if (!Utils::validateCardSerial($serialNumber, $provider)) {
        throw new Exception('Invalid card serial format');
    }
    
    if (!Utils::validateCardCode($cardCode, $provider)) {
        throw new Exception('Invalid card code format');
    }
    
    // Check for duplicate card
    $existingCard = $database->fetchOne(
        "SELECT id FROM card_transactions WHERE serial_number = ? AND card_code = ?",
        [$serialNumber, $cardCode]
    );
    
    if ($existingCard) {
        throw new Exception('Card has already been used');
    }
    
    // Save card transaction
    $cardData = [
        'transaction_id' => $transactionId,
        'provider' => $provider,
        'card_value' => $cardValue,
        'serial_number' => $serialNumber,
        'card_code' => $cardCode,
        'verification_status' => 'pending'
    ];
    
    $database->insert('card_transactions', $cardData);
    
    // In real implementation, you would call card verification API here
    // For now, we'll simulate it
    $verificationResult = simulateCardVerification($provider, $serialNumber, $cardCode, $cardValue);
    
    return [
        'type' => 'card',
        'provider' => $provider,
        'card_value' => $cardValue,
        'verification_status' => $verificationResult['status'],
        'message' => $verificationResult['message']
    ];
}

function handleBankTransfer($database, $transactionId, $paymentData, $amount) {
    // Get bank settings
    $bankSettings = [
        'bank_name' => 'Vietcombank',
        'account_number' => '**********',
        'account_name' => 'NGUYEN VAN A'
    ];
    
    $transferContent = "NAP {$paymentData['username']} " . date('dmHi');
    
    // Save bank transfer
    $transferData = [
        'transaction_id' => $transactionId,
        'bank_name' => $bankSettings['bank_name'],
        'account_number' => $bankSettings['account_number'],
        'account_name' => $bankSettings['account_name'],
        'transfer_content' => $transferContent,
        'expected_amount' => $amount,
        'verification_status' => 'pending'
    ];
    
    $database->insert('bank_transfers', $transferData);
    
    return [
        'type' => 'bank_transfer',
        'bank_name' => $bankSettings['bank_name'],
        'account_number' => $bankSettings['account_number'],
        'account_name' => $bankSettings['account_name'],
        'amount' => $amount,
        'transfer_content' => $transferContent,
        'formatted_amount' => Utils::formatCurrency($amount)
    ];
}

function simulateCardVerification($provider, $serial, $code, $value) {
    // Simulate verification delay
    usleep(200000); // 0.2 seconds
    
    // Mock verification logic
    $success = rand(0, 10) > 2; // 80% success rate
    
    if ($success) {
        return [
            'status' => 'valid',
            'message' => 'Card verified successfully'
        ];
    } else {
        return [
            'status' => 'invalid',
            'message' => 'Card verification failed'
        ];
    }
}

function getTransactions($database) {
    $username = $_GET['username'] ?? null;
    $limit = min((int)($_GET['limit'] ?? 20), 100);
    $offset = max((int)($_GET['offset'] ?? 0), 0);
    
    $whereClause = '';
    $params = [];
    
    if ($username) {
        $whereClause = 'WHERE t.tiktok_username = ?';
        $params[] = $username;
    }
    
    $sql = "SELECT t.*, cp.name as package_name, pm.name as payment_method_name
            FROM transactions t
            LEFT JOIN coin_packages cp ON t.package_id = cp.id
            LEFT JOIN payment_methods pm ON t.payment_method_id = pm.id
            {$whereClause}
            ORDER BY t.created_at DESC
            LIMIT {$limit} OFFSET {$offset}";
    
    $transactions = $database->fetchAll($sql, $params);
    
    // Format transactions
    $formattedTransactions = array_map(function($transaction) {
        return [
            'id' => (int)$transaction['id'],
            'transaction_code' => $transaction['transaction_code'],
            'tiktok_username' => $transaction['tiktok_username'],
            'package_name' => $transaction['package_name'],
            'coin_amount' => (int)$transaction['coin_amount'],
            'amount' => (float)$transaction['amount'],
            'final_amount' => (float)$transaction['final_amount'],
            'status' => $transaction['status'],
            'payment_method' => $transaction['payment_method_name'],
            'created_at' => $transaction['created_at'],
            'processed_at' => $transaction['processed_at']
        ];
    }, $transactions);
    
    ApiResponse::success($formattedTransactions, 'Transactions retrieved successfully');
}
?>
