# TikTok Coin Recharge System

Hệ thống nạp xu TikTok với giao diện đẹp, hỗ trợ thanh toán qua thẻ cào và chuyển khoản ngân hàng.

## 🌟 Tính năng

- ✅ Giao diện hiện đại, responsive
- ✅ Kiểm tra username TikTok
- ✅ Nhiều gói xu với giá khác nhau
- ✅ Thanh toán qua thẻ cào (Viettel, VinaPhone, Mobifone, Zing, Garena)
- ✅ Thanh toán qua chuyển khoản ngân hàng
- ✅ Lịch sử giao dịch
- ✅ API RESTful
- ✅ Database MySQL
- ✅ Rate limiting
- ✅ Validation và security

## 🛠️ Công nghệ sử dụng

- **Frontend**: HTML5, CSS3, JavaScript (Vanilla)
- **Backend**: PHP 7.4+
- **Database**: MySQL 8.0+
- **Web Server**: Apache/Nginx

## 📁 Cấu trúc thư mục

```
ScamTiktok/
├── index.html              # Trang chính
├── assets/
│   ├── css/
│   │   └── style.css       # CSS chính
│   ├── js/
│   │   └── main.js         # JavaScript chính
│   └── images/             # Hình ảnh (logo, icons)
├── api/
│   ├── config/
│   │   └── database.php    # Cấu hình database
│   └── endpoints/
│       ├── packages.php    # API gói xu
│       ├── verify-username.php # API kiểm tra username
│       └── transactions.php    # API giao dịch
├── database/
│   └── setup.sql          # Script tạo database
└── README.md
```

## 🚀 Cài đặt

### 1. Yêu cầu hệ thống

- PHP 7.4 hoặc cao hơn
- MySQL 8.0 hoặc cao hơn
- Web server (Apache/Nginx)
- Extension PHP: PDO, PDO_MySQL

### 2. Cài đặt database

```sql
-- Tạo database
mysql -u root -p < database/setup.sql
```

### 3. Cấu hình database

Chỉnh sửa file `api/config/database.php`:

```php
private $host = 'localhost';
private $db_name = 'tiktok_recharge';
private $username = 'root';
private $password = 'your_password';
```

### 4. Cấu hình web server

#### Apache (.htaccess)

```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/endpoints/$1 [QSA,L]
```

#### Nginx

```nginx
location /api/ {
    try_files $uri $uri/ /api/endpoints/$1;
}
```

### 5. Permissions

```bash
chmod 755 api/
chmod 644 api/config/database.php
chmod 644 api/endpoints/*.php
```

## 🔧 Cấu hình

### Database Settings

Chỉnh sửa các cài đặt trong bảng `system_settings`:

```sql
UPDATE system_settings SET setting_value = 'Your Bank Name' WHERE setting_key = 'bank_name';
UPDATE system_settings SET setting_value = '**********' WHERE setting_key = 'bank_account_number';
UPDATE system_settings SET setting_value = 'ACCOUNT HOLDER NAME' WHERE setting_key = 'bank_account_name';
```

### Payment Methods

Thêm/sửa phương thức thanh toán trong bảng `payment_methods`:

```sql
-- Cập nhật phí thẻ cào
UPDATE payment_methods SET fee_percent = 5.0 WHERE provider = 'viettel';
```

### Coin Packages

Thêm/sửa gói xu trong bảng `coin_packages`:

```sql
INSERT INTO coin_packages (name, coin_amount, price, sort_order) 
VALUES ('Gói Custom', 1000, 200000, 7);
```

## 📱 API Documentation

### 1. Get Packages

```http
GET /api/endpoints/packages.php
```

**Response:**
```json
{
  "success": true,
  "message": "Packages loaded successfully",
  "data": [
    {
      "id": 1,
      "name": "Gói Starter",
      "coins": 100,
      "price": 25000,
      "discount": 0,
      "formatted_price": "25,000 VNĐ",
      "formatted_coins": "100 xu"
    }
  ]
}
```

### 2. Verify Username

```http
POST /api/endpoints/verify-username.php
Content-Type: application/json

{
  "username": "example_user"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Username verified successfully",
  "data": {
    "username": "example_user",
    "display_name": "@example_user",
    "is_verified": false,
    "follower_count": 1500,
    "status": "active",
    "cached": false
  }
}
```

### 3. Create Transaction

```http
POST /api/endpoints/transactions.php
Content-Type: application/json

{
  "tiktok_username": "example_user",
  "package_id": 1,
  "payment_method": "card",
  "payment_data": {
    "provider": "viettel",
    "card_value": 50000,
    "serial_number": "**********1",
    "card_code": "**********12345"
  }
}
```

## 🔒 Bảo mật

- Rate limiting: 100 requests/hour cho packages, 30 requests/hour cho verify username
- Input validation và sanitization
- SQL injection protection với prepared statements
- XSS protection
- CORS headers

## 🎨 Customization

### Thay đổi theme colors

Chỉnh sửa CSS variables trong `assets/css/style.css`:

```css
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-color: #4ecdc4;
  --success-color: #56ab2f;
}
```

### Thêm logo và hình ảnh

Thêm các file hình ảnh vào thư mục `assets/images/`:
- `tiktok.png` - Logo TikTok
- `viettel.png` - Logo Viettel
- `vinaphone.png` - Logo VinaPhone
- `mobifone.png` - Logo Mobifone
- `zing.png` - Logo Zing
- `garena.png` - Logo Garena

## 🐛 Troubleshooting

### Lỗi kết nối database

1. Kiểm tra thông tin kết nối trong `api/config/database.php`
2. Đảm bảo MySQL service đang chạy
3. Kiểm tra permissions của user database

### API không hoạt động

1. Kiểm tra web server configuration
2. Đảm bảo mod_rewrite được enable (Apache)
3. Kiểm tra file permissions

### CORS errors

Thêm headers CORS trong web server config hoặc sử dụng function `setCorsHeaders()` trong PHP.

## 📄 License

MIT License - Xem file LICENSE để biết thêm chi tiết.

## 👥 Đóng góp

1. Fork project
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Tạo Pull Request

## 📞 Hỗ trợ

- Email: <EMAIL>
- Telegram: @your_telegram
- Issues: [GitHub Issues](https://github.com/your-repo/issues)

---

**Lưu ý**: Đây là project demo. Trong môi trường production, cần thêm các tính năng bảo mật và tối ưu hóa khác.
