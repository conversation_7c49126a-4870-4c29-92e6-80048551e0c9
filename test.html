<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test TikTok Recharge System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .result {
            background: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #4CAF50;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .error {
            border-left-color: #f44336;
            background: #ffebee;
        }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🧪 Test TikTok Recharge System</h1>
    
    <div class="test-section">
        <h2>1. Test API Packages</h2>
        <button class="test-button" onclick="testPackages()">Test Get Packages</button>
        <div id="packages-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Username Verification</h2>
        <input type="text" id="test-username" placeholder="Nhập username để test" value="testuser123">
        <button class="test-button" onclick="testUsername()">Test Verify Username</button>
        <div id="username-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Card Transaction</h2>
        <div>
            <input type="text" id="card-username" placeholder="TikTok Username" value="testuser123">
            <select id="card-package">
                <option value="1">Gói 1 - 100 xu</option>
                <option value="2">Gói 2 - 330 xu</option>
                <option value="3">Gói 3 - 660 xu</option>
            </select>
        </div>
        <div>
            <select id="card-provider">
                <option value="viettel">Viettel</option>
                <option value="vinaphone">VinaPhone</option>
                <option value="mobifone">Mobifone</option>
            </select>
            <input type="number" id="card-value" placeholder="Mệnh giá" value="50000">
        </div>
        <div>
            <input type="text" id="card-serial" placeholder="Số seri" value="**************">
            <input type="text" id="card-code" placeholder="Mã thẻ" value="***************">
        </div>
        <button class="test-button" onclick="testCardTransaction()">Test Card Transaction</button>
        <div id="card-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>4. Test Bank Transfer</h2>
        <div>
            <input type="text" id="transfer-username" placeholder="TikTok Username" value="testuser123">
            <select id="transfer-package">
                <option value="1">Gói 1 - 100 xu</option>
                <option value="2">Gói 2 - 330 xu</option>
                <option value="3">Gói 3 - 660 xu</option>
            </select>
        </div>
        <button class="test-button" onclick="testBankTransfer()">Test Bank Transfer</button>
        <div id="transfer-result" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>5. Test Get Transactions</h2>
        <input type="text" id="transaction-username" placeholder="Username (optional)" value="">
        <button class="test-button" onclick="testGetTransactions()">Test Get Transactions</button>
        <div id="transactions-result" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = window.location.origin + '/api/endpoints/';

        async function testPackages() {
            const resultDiv = document.getElementById('packages-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(API_BASE + 'packages.php');
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (!result.success) {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testUsername() {
            const username = document.getElementById('test-username').value;
            const resultDiv = document.getElementById('username-result');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            resultDiv.className = 'result';

            try {
                const response = await fetch(API_BASE + 'verify-username.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username: username })
                });
                
                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (!result.success) {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testCardTransaction() {
            const resultDiv = document.getElementById('card-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            resultDiv.className = 'result';

            const data = {
                tiktok_username: document.getElementById('card-username').value,
                package_id: parseInt(document.getElementById('card-package').value),
                payment_method: 'card',
                payment_data: {
                    provider: document.getElementById('card-provider').value,
                    card_value: parseFloat(document.getElementById('card-value').value),
                    serial_number: document.getElementById('card-serial').value,
                    card_code: document.getElementById('card-code').value
                }
            };

            try {
                const response = await fetch(API_BASE + 'transactions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (!result.success) {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testBankTransfer() {
            const resultDiv = document.getElementById('transfer-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            resultDiv.className = 'result';

            const data = {
                tiktok_username: document.getElementById('transfer-username').value,
                package_id: parseInt(document.getElementById('transfer-package').value),
                payment_method: 'bank_transfer',
                payment_data: {
                    username: document.getElementById('transfer-username').value
                }
            };

            try {
                const response = await fetch(API_BASE + 'transactions.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (!result.success) {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        async function testGetTransactions() {
            const username = document.getElementById('transaction-username').value;
            const resultDiv = document.getElementById('transactions-result');
            
            resultDiv.style.display = 'block';
            resultDiv.textContent = 'Loading...';
            resultDiv.className = 'result';

            let url = API_BASE + 'transactions.php';
            if (username) {
                url += '?username=' + encodeURIComponent(username);
            }

            try {
                const response = await fetch(url);
                const result = await response.json();
                
                resultDiv.textContent = JSON.stringify(result, null, 2);
                
                if (!result.success) {
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = 'Error: ' + error.message;
                resultDiv.className = 'result error';
            }
        }

        // Auto test on load
        window.addEventListener('load', function() {
            console.log('Test page loaded. You can now test the APIs.');
        });
    </script>
</body>
</html>
