/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    color: #fff;
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 8px;
}

.logo-text {
    font-size: 24px;
    font-weight: bold;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-menu {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.user-avatar:hover {
    background: rgba(255, 255, 255, 0.2);
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    min-width: 200px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 10px;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu ul {
    list-style: none;
    padding: 10px 0;
}

.dropdown-menu li {
    padding: 0;
}

.dropdown-menu li.divider {
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
    margin: 8px 0;
}

.dropdown-menu a {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    color: #fff;
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-menu a:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Main Content */
.main-content {
    padding: 30px 0;
    flex: 1;
}

.content-wrapper {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 30px;
    align-items: start;
}

/* Step Sections */
.step-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 25px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.step-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #fff;
}

/* Username Form */
.username-form {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #4ecdc4;
    background: rgba(255, 255, 255, 0.15);
}

.form-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: #fff;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: #fff;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
}

.btn-full {
    width: 100%;
}

/* Package Grid */
.package-loading {
    text-align: center;
    padding: 40px;
    color: rgba(255, 255, 255, 0.7);
}

.package-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.package-item {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.package-item:hover {
    border-color: #4ecdc4;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.package-item.selected {
    border-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.2);
}

.package-coins {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 8px;
}

.package-price {
    font-size: 16px;
    color: #4ecdc4;
    font-weight: 600;
}

/* Payment Tabs */
.payment-tabs {
    display: flex;
    margin-bottom: 20px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    padding: 12px;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: #fff;
}

.payment-form {
    display: none;
}

.payment-form.active {
    display: block;
}

/* Provider Grid */
.provider-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.provider-item {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 15px 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.provider-item:hover {
    border-color: #4ecdc4;
}

.provider-item.selected {
    border-color: #4ecdc4;
    background: rgba(78, 205, 196, 0.2);
}

.provider-item img {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
    border-radius: 4px;
}

.provider-item span {
    display: block;
    font-size: 12px;
    font-weight: 500;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
}

.form-select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-select:focus {
    outline: none;
    border-color: #4ecdc4;
}

.form-select option {
    background: #333;
    color: #fff;
}

/* Right Sidebar */
.right-sidebar {
    position: sticky;
    top: 100px;
}

.order-summary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.summary-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #fff;
}

.summary-placeholder {
    color: rgba(255, 255, 255, 0.6);
    text-align: center;
    padding: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    font-weight: 600;
    font-size: 16px;
}

/* Transaction Table */
.transaction-table {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    overflow: hidden;
}

.no-transactions {
    text-align: center;
    padding: 40px;
    color: rgba(255, 255, 255, 0.6);
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

th {
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 20px 0;
    margin-top: 50px;
}

.footer-content {
    text-align: center;
}

.footer-content p {
    margin-bottom: 5px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-content a {
    color: #4ecdc4;
    text-decoration: none;
}

.disclaimer {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    margin: 0;
    color: #fff;
}

.modal-close {
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.modal-body {
    padding: 25px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-wrapper {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .right-sidebar {
        order: -1;
        position: static;
    }
    
    .username-form {
        flex-direction: column;
    }
    
    .provider-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .package-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        padding: 10px 0;
    }
    
    .logo-text {
        font-size: 20px;
    }
    
    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .provider-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .step-section {
        padding: 20px 15px;
    }
    
    .order-summary {
        padding: 20px 15px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-section {
    animation: fadeIn 0.6s ease-out;
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.fa-spinner {
    animation: spin 1s linear infinite;
}

/* Success/Error States */
.username-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 6px;
    font-size: 14px;
}

.username-result.success {
    background: rgba(86, 171, 47, 0.2);
    border: 1px solid rgba(86, 171, 47, 0.5);
    color: #a8e6cf;
}

.username-result.error {
    background: rgba(231, 76, 60, 0.2);
    border: 1px solid rgba(231, 76, 60, 0.5);
    color: #ff6b6b;
}

/* Transfer Info */
.transfer-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
}

.transfer-info strong {
    color: #4ecdc4;
    font-size: 18px;
}
